const Button = require('./Button.js')

/**
 * 失败页面组件
 * 负责渲染美化的游戏失败页面和管理失败动画
 */
class LoseScreen {
  constructor(canvasSize, animationManager, particleSystem) {
    this.canvasSize = canvasSize
    this.animationManager = animationManager
    this.particleSystem = particleSystem
    
    // 动画状态
    this.isVisible = false
    this.animationStartTime = 0
    this.animationDuration = 2500 // 2.5秒动画
    this.canSkip = false
    this.skipDelay = 800 // 0.8秒后可以跳过
    
    // UI元素状态
    this.titleScale = 0
    this.titleAlpha = 0
    this.titleShake = 0
    this.statsAlpha = 0
    this.buttonAlpha = 0
    this.backgroundAlpha = 0
    
    // 统计信息动画
    this.animatedStats = {
      time: 0,
      score: 0,
      moves: 0
    }
    this.targetStats = {
      time: 0,
      score: 0,
      moves: 0
    }
    
    // 按钮
    this.retryButton = null
    this.backButton = null
    this.buttonHover = null
    
    // 回调函数
    this.callbacks = {
      onRetry: null,
      onBack: null
    }
    
    this.initButtons()
  }

  /**
   * 初始化按钮
   */
  initButtons() {
    const buttonWidth = 120
    const buttonHeight = 45
    const buttonSpacing = 30
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    // 重试按钮
    this.retryButton = new Button(
      centerX - buttonWidth - buttonSpacing / 2,
      centerY + 120,
      buttonWidth,
      buttonHeight,
      '重新开始',
      () => {
        if (this.callbacks.onRetry) {
          this.callbacks.onRetry()
        }
      }
    )
    this.retryButton.setTheme('blue')
    
    // 返回按钮
    this.backButton = new Button(
      centerX + buttonSpacing / 2,
      centerY + 120,
      buttonWidth,
      buttonHeight,
      '返回选关',
      () => {
        if (this.callbacks.onBack) {
          this.callbacks.onBack()
        }
      }
    )
    this.backButton.setTheme('gray')
  }

  /**
   * 显示失败页面
   * @param {Object} stats - 游戏统计信息 {time, score, moves}
   * @param {Object} callbacks - 回调函数 {onRetry, onBack}
   */
  show(stats, callbacks = {}) {
    this.isVisible = true
    this.animationStartTime = Date.now()
    this.canSkip = false
    
    // 设置目标统计数据
    this.targetStats = { ...stats }
    this.animatedStats = { time: 0, score: 0, moves: 0 }
    
    // 设置回调函数
    this.callbacks = callbacks
    
    // 重置动画状态
    this.titleScale = 0
    this.titleAlpha = 0
    this.titleShake = 0
    this.statsAlpha = 0
    this.buttonAlpha = 0
    this.backgroundAlpha = 0
    
    // 启动失败效果
    this.startFailureEffects()
    
    // 设置跳过延迟
    setTimeout(() => {
      this.canSkip = true
    }, this.skipDelay)
  }

  /**
   * 隐藏失败页面
   */
  hide() {
    this.isVisible = false
    this.particleSystem.clear()
  }

  /**
   * 启动失败效果
   */
  startFailureEffects() {
    // 添加一些暗色粒子效果
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2

    // 创建失败烟花效果（使用暗色调，减少粒子数量）
    const darkColors = ['#8B0000', '#A52A2A', '#B22222', '#DC143C', '#FF6B6B']
    this.particleSystem.createFirework(centerX, centerY - 50, 8, darkColors)

    // 添加一些额外的暗色粒子（延迟时间缩短，粒子数量减少）
    setTimeout(() => {
      this.particleSystem.createFirework(
        centerX + (Math.random() - 0.5) * 80,
        centerY + (Math.random() - 0.5) * 80,
        5,
        darkColors
      )
    }, 150)
  }

  /**
   * 更新动画
   * @param {number} deltaTime - 时间差
   */
  update(deltaTime) {
    if (!this.isVisible) return

    // 更新粒子系统
    this.particleSystem.update(deltaTime)

    const elapsed = Date.now() - this.animationStartTime
    const progress = Math.min(elapsed / this.animationDuration, 1)

    // 背景遮罩动画
    this.backgroundAlpha = this.easeOutCubic(Math.min(elapsed / 300, 1))
    
    // 标题动画 (300ms - 800ms)
    if (elapsed > 300) {
      const titleProgress = Math.min((elapsed - 300) / 500, 1)
      this.titleScale = this.easeOutBack(titleProgress)
      this.titleAlpha = this.easeOutCubic(titleProgress)
      
      // 添加震动效果
      if (titleProgress < 0.3) {
        this.titleShake = (0.3 - titleProgress) * 10
      } else {
        this.titleShake = 0
      }
    }
    
    // 统计信息动画 (800ms - 1500ms)
    if (elapsed > 800) {
      const statsProgress = Math.min((elapsed - 800) / 700, 1)
      this.statsAlpha = this.easeOutCubic(statsProgress)
      
      // 数字计数动画
      this.animatedStats.time = Math.floor(this.targetStats.time * statsProgress)
      this.animatedStats.score = Math.floor(this.targetStats.score * statsProgress)
      this.animatedStats.moves = Math.floor(this.targetStats.moves * statsProgress)
    }
    
    // 按钮动画 (1500ms - 2000ms)
    if (elapsed > 1500) {
      const buttonProgress = Math.min((elapsed - 1500) / 500, 1)
      this.buttonAlpha = this.easeOutCubic(buttonProgress)
    }

    // 动画完成后清理粒子（3秒后）
    if (elapsed > 3000) {
      this.particleSystem.clear()
    }
  }

  /**
   * 缓动函数 - 三次方缓出
   */
  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3)
  }

  /**
   * 缓动函数 - 回弹缓出
   */
  easeOutBack(t) {
    const c1 = 1.70158
    const c3 = c1 + 1
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2)
  }

  /**
   * 渲染失败页面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isVisible) return
    
    ctx.save()
    
    // 渲染背景
    this.renderBackground(ctx)
    
    // 渲染粒子效果
    this.particleSystem.render(ctx)
    
    // 渲染标题
    this.renderTitle(ctx)
    
    // 渲染统计信息
    this.renderStats(ctx)
    
    // 渲染按钮
    this.renderButtons(ctx)
    
    // 渲染跳过提示
    this.renderSkipHint(ctx)
    
    ctx.restore()
  }

  /**
   * 渲染背景
   */
  renderBackground(ctx) {
    ctx.globalAlpha = this.backgroundAlpha * 0.8
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    ctx.fillRect(0, 0, this.canvasSize.screenWidth, this.canvasSize.screenHeight)
    ctx.globalAlpha = 1
  }

  /**
   * 渲染标题
   */
  renderTitle(ctx) {
    if (this.titleAlpha <= 0) return
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    ctx.save()
    ctx.globalAlpha = this.titleAlpha
    
    // 应用震动效果
    const shakeX = (Math.random() - 0.5) * this.titleShake
    const shakeY = (Math.random() - 0.5) * this.titleShake
    
    ctx.translate(centerX + shakeX, centerY - 60 + shakeY)
    ctx.scale(this.titleScale, this.titleScale)
    
    // 标题阴影
    ctx.shadowColor = 'rgba(139, 0, 0, 0.5)'
    ctx.shadowBlur = 10
    ctx.shadowOffsetX = 3
    ctx.shadowOffsetY = 3
    
    // 标题描边
    ctx.strokeStyle = '#8B0000'
    ctx.lineWidth = 4
    ctx.font = 'bold 42px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.strokeText('游戏失败', 0, 0)
    
    // 标题填充
    ctx.fillStyle = '#FF6B6B'
    ctx.fillText('游戏失败', 0, 0)
    
    ctx.restore()
  }

  /**
   * 渲染统计信息
   */
  renderStats(ctx) {
    if (this.statsAlpha <= 0) return
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    ctx.save()
    ctx.globalAlpha = this.statsAlpha
    ctx.fillStyle = '#FFFFFF'
    ctx.font = '20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    // 添加文字阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    
    ctx.fillText(`用时: ${this.animatedStats.time}秒`, centerX, centerY + 20)
    ctx.fillText(`得分: ${this.animatedStats.score}`, centerX, centerY + 50)
    ctx.fillText(`步数: ${this.animatedStats.moves}`, centerX, centerY + 80)
    
    ctx.restore()
  }

  /**
   * 渲染按钮
   */
  renderButtons(ctx) {
    if (this.buttonAlpha <= 0) return
    
    ctx.save()
    ctx.globalAlpha = this.buttonAlpha
    
    this.retryButton.render(ctx)
    this.backButton.render(ctx)
    
    ctx.restore()
  }

  /**
   * 渲染跳过提示
   */
  renderSkipHint(ctx) {
    if (!this.canSkip) return
    
    const centerX = this.canvasSize.screenWidth / 2
    const centerY = this.canvasSize.screenHeight / 2
    
    ctx.save()
    ctx.globalAlpha = 0.6
    ctx.fillStyle = '#CCCCCC'
    ctx.font = '14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    ctx.fillText('点击任意位置跳过动画', centerX, centerY + 200)
    
    ctx.restore()
  }

  /**
   * 处理触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleTouch(x, y) {
    if (!this.isVisible) return false
    
    // 如果可以跳过动画，点击任意位置跳过
    if (this.canSkip) {
      this.skipAnimation()
    }
    
    // 检查按钮点击
    if (this.buttonAlpha > 0.5) {
      if (this.retryButton.containsPoint(x, y)) {
        this.retryButton.handleClick(x, y)
        return true
      }

      if (this.backButton.containsPoint(x, y)) {
        this.backButton.handleClick(x, y)
        return true
      }
    }
    
    return true // 阻止事件传播
  }

  /**
   * 跳过动画
   */
  skipAnimation() {
    if (!this.canSkip) return
    
    // 立即完成所有动画
    this.titleScale = 1
    this.titleAlpha = 1
    this.titleShake = 0
    this.statsAlpha = 1
    this.buttonAlpha = 1
    this.backgroundAlpha = 1
    
    // 完成统计数字动画
    this.animatedStats = { ...this.targetStats }
    
    // 清除粒子效果
    this.particleSystem.clear()
  }
}

// CommonJS导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LoseScreen
}
